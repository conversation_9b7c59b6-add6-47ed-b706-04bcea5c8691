import { Link } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white pt-16 pb-6 relative z-10">
      <div className="max-w-6xl mx-auto px-5 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
        {/* TRYPINDIA Section */}
        <div className="space-y-5">
          <h3 className="text-2xl font-bold">TRYPINDIA</h3>
          <p className="text-gray-300 leading-relaxed">
            Your trusted travel partner for exploring the beauty of North East India. We create unforgettable travel experiences.
          </p>
          <div className="flex gap-4">
            <a
              href="https://facebook.com/trypindia"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Visit our Facebook page"
              className="w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500 to-indigo-600 flex items-center justify-center text-black text-xl transition-all duration-300 hover:-translate-y-1 hover:scale-110 hover:shadow-lg hover:shadow-yellow-500/30 relative overflow-hidden border-2 border-transparent hover:border-yellow-500"
            >
              <i className="fab fa-facebook-f"></i>
              <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent opacity-0 hover:opacity-100 hover:animate-shine"></span>
            </a>
            <a
              href="https://x.com/trypindia"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Visit our X (Twitter) page"
              className="w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500 to-indigo-600 flex items-center justify-center text-black text-xl transition-all duration-300 hover:-translate-y-1 hover:scale-110 hover:shadow-lg hover:shadow-yellow-500/30 relative overflow-hidden border-2 border-transparent hover:border-yellow-500"
            >
              <i className="fab fa-x-twitter"></i>
              <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent opacity-0 hover:opacity-100 hover:animate-shine"></span>
            </a>
            <a
              href="https://instagram.com/trypindia"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Visit our Instagram page"
              className="w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500 to-indigo-600 flex items-center justify-center text-black text-xl transition-all duration-300 hover:-translate-y-1 hover:scale-110 hover:shadow-lg hover:shadow-yellow-500/30 relative overflow-hidden border-2 border-transparent hover:border-yellow-500"
            >
              <i className="fab fa-instagram"></i>
              <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent opacity-0 hover:opacity-100 hover:animate-shine"></span>
            </a>
          </div>
        </div>

        {/* Quick Links Section */}
        <div className="space-y-5">
          <h4 className="text-xl font-semibold relative inline-block pb-3 mb-2">
            Quick Links
            <span className="absolute left-0 bottom-0 w-12 h-0.5 bg-indigo-500"></span>
          </h4>
          <ul className="space-y-3">
            <li>
              <Link to="/" className="text-gray-300 hover:text-indigo-400 transition-colors duration-300 hover:pl-2 block">
                Home
              </Link>
            </li>
            <li>
              <Link to="/plan" className="text-gray-300 hover:text-indigo-400 transition-colors duration-300 hover:pl-2 block">
                Plan Your Trip
              </Link>
            </li>
            <li>
              <Link to="/about" className="text-gray-300 hover:text-indigo-400 transition-colors duration-300 hover:pl-2 block">
                About Us
              </Link>
            </li>
            <li>
              <Link to="/gallery" className="text-gray-300 hover:text-indigo-400 transition-colors duration-300 hover:pl-2 block">
                Gallery
              </Link>
            </li>
            <li>
              <Link to="/contact" className="text-gray-300 hover:text-indigo-400 transition-colors duration-300 hover:pl-2 block">
                Contact
              </Link>
            </li>
          </ul>
        </div>

        {/* Contact Info Section */}
        <div className="space-y-5">
          <h4 className="text-xl font-semibold relative inline-block pb-3 mb-2">
            Contact Info
            <span className="absolute left-0 bottom-0 w-12 h-0.5 bg-indigo-500"></span>
          </h4>
          <ul className="space-y-4">
            <li className="flex items-center gap-3 text-gray-300">
              <i className="fas fa-phone text-indigo-500"></i>
              <span>+91 8584807189</span>
            </li>
            <li className="flex items-center gap-3 text-gray-300">
              <i className="fas fa-envelope text-indigo-500"></i>
              <span><EMAIL></span>
            </li>
          </ul>
        </div>

        {/* Newsletter Section */}
        <div className="space-y-5">
          <h4 className="text-xl font-semibold relative inline-block pb-3 mb-2">
            Newsletter
            <span className="absolute left-0 bottom-0 w-12 h-0.5 bg-indigo-500"></span>
          </h4>
          <p className="text-gray-300">Subscribe to our newsletter for travel updates and special offers</p>
          <form className="flex gap-3 mt-4">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-5 py-3 rounded-full border border-white/10 bg-white/5 text-white placeholder-gray-500 focus:outline-none focus:border-yellow-500 focus:ring-2 focus:ring-yellow-500/10"
            />
            <button
              type="submit"
              aria-label="Subscribe to newsletter"
              className="w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500 to-indigo-600 text-black flex items-center justify-center transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-yellow-500/20"
            >
              <i className="fas fa-paper-plane text-lg"></i>
            </button>
          </form>
        </div>
      </div>

      {/* Footer Bottom */}
      <div className="mt-10 pt-6 border-t border-gray-800 text-center">
        <p className="text-gray-300">© 2025 TRYPINDIA. All rights reserved.</p>
      </div>
    </footer>
  );
};

export default Footer;