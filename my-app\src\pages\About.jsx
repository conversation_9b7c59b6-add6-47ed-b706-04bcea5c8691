import React, { useState, useEffect } from 'react';
import profile1 from '../assets/photos/profile1.webp';
import profile2 from '../assets/photos/profile2.webp';
import profile3 from '../assets/photos/profile3.webp';
import profile4 from '../assets/photos/profile4.webp';
import profile5 from '../assets/photos/profile5.webp';
import profile6 from '../assets/photos/profile6.webp';
import forest from '../assets/photos/forest.webp';
import about_story from '../assets/photos/about_story.webp';
import about_motto from '../assets/photos/about_motto.webp';

const About = () => {
  const [counts, setCounts] = useState({
    customers: 0,
    trips: 0,
    localnetwork: 0,
    stays: 0
  });

  useEffect(() => {
    const duration = 10000;
    const steps = 50;
    const interval = duration / steps;

    const targetCounts = {
      customers: 200,
      trips: 50,
      localnetwork: 15,
      stays: 25
    };

    let currentStep = 0;

    const timer = setInterval(() => {
      currentStep++;

      setCounts({
        customers: Math.floor((targetCounts.customers / steps) * currentStep),
        trips: Math.floor((targetCounts.trips / steps) * currentStep),
        localnetwork: Math.floor((targetCounts.localnetwork / steps) * currentStep),
        stays: Math.floor((targetCounts.stays / steps) * currentStep)
      });

      if (currentStep === steps) {
        setCounts(targetCounts);
        clearInterval(timer);
      }
    }, interval);

    return () => clearInterval(timer);
  }, []);

  const reviews = [
    {
      name: 'Rashi Gupta',
      rating: 5,
      review: 'Amazing experience! The team went above and beyond to make our trip memorable.',
      image: profile1,
      date: 'December 2023'
    },
    {
      name: 'Hemant Pradhan',
      rating: 5,
      review: 'Perfect organization and wonderful destinations. Will definitely book again!',
      image: profile2,
      date: 'January 2024'
    },
    {
      name: 'Ankit Kumar',
      rating: 4,
      review: 'The best travel experience I have had. The local insights were invaluable.',
      image: profile3,
      date: 'February 2024'
    }
  ];

  return (
    <div className="pt-0 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen relative font-['Poppins'] overflow-hidden w-full">
      {/* Hero Section */}
      <div className="relative py-20 mb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left column - Text content */}
            <div className="text-left">
              <div className="inline-block mb-4 mt-4">
                <span className="bg-indigo-100 text-indigo-800 text-xs font-semibold px-3 py-1 rounded-full uppercase tracking-wider">About Us</span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Discover the <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-blue-500">Magic</span> of North East India
              </h1>

              <p className="text-lg text-gray-600 leading-relaxed mb-8 max-w-xl">
                At Trypindia, we're dedicated to creating authentic travel experiences that showcase the true beauty and culture of North East India. Join us on a journey of discovery.
              </p>

              <div className="flex flex-wrap gap-4">
                <a href="#our-story" className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium px-6 py-3 rounded-lg transition-colors duration-300 flex items-center">
                  <i className="fas fa-book-open mr-2"></i>
                  <span>Our Story</span>
                </a>
                <a href="#testimonials" className="bg-white hover:bg-gray-100 text-indigo-600 font-medium px-6 py-3 rounded-lg border border-gray-200 transition-colors duration-300 flex items-center">
                  <i className="fas fa-star mr-2"></i>
                  <span>Testimonials</span>
                </a>
              </div>

              <div className="mt-12 flex items-center">
                <div className="flex -space-x-2 mr-4">
                  <img src={profile4} alt="Team member" className="w-10 h-10 rounded-full border-2 border-white" />
                  <img src={profile5} alt="Team member" className="w-10 h-10 rounded-full border-2 border-white" />
                  <img src={profile6} alt="Team member" className="w-10 h-10 rounded-full border-2 border-white" />
                </div>
                <p className="text-sm text-gray-500">Trusted by <span className="font-semibold text-indigo-600">200+</span> travelers</p>
              </div>
            </div>

            {/* Right column - Image */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-2xl opacity-20 blur-2xl transform -rotate-6"></div>
              <div className="relative bg-white p-3 rounded-2xl shadow-xl">
                <div className="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden">
                  <img
                    src={forest}
                    alt="North East India Landscape"
                    className="w-full h-full object-cover"
                    width="600"
                    height="450"
                    loading="eager"
                  />
                </div>
                <div className="absolute -bottom-4 -right-4 bg-white px-4 py-2 rounded-lg shadow-lg flex items-center">
                  <div className="bg-indigo-100 p-2 rounded-full mr-3">
                    <i className="fas fa-map-marker-alt text-indigo-600"></i>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Featured Destination</p>
                    <p className="text-sm font-medium text-gray-900">Darjeeling, West Bengal</p>
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute -top-6 -left-6 w-12 h-12 bg-indigo-100 rounded-lg rotate-12 z-[-1]"></div>
              <div className="absolute -bottom-6 -right-6 w-20 h-20 bg-blue-100 rounded-full z-[-1]"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Story Section */}
      <section id="our-story" className="relative z-[2] px-4 py-16">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <span className="bg-indigo-100 text-indigo-800 text-xs font-semibold px-3 py-1 rounded-full uppercase tracking-wider">Our Journey</span>
            <h2 className="text-3xl md:text-4xl font-bold mt-4 mb-6 text-gray-900">
              The Story Behind <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-blue-500">Trypindia</span>
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              How a passion for authentic travel experiences led to creating a company that connects travelers with the true essence of North East India.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 items-center">
            <div className="lg:col-span-7 relative">
              <div className="absolute -top-6 -left-6 w-24 h-24 bg-indigo-100 rounded-full z-[-1]"></div>
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-blue-100 rounded-full z-[-1]"></div>

              <div className="relative overflow-hidden rounded-xl shadow-xl">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-600/20 to-blue-500/20 mix-blend-multiply"></div>
                <img
                  src={about_story}
                  alt="Our Story"
                  className="w-full h-full object-cover rounded-xl"
                  width="800"
                  height="600"
                  loading="lazy"
                />
                <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/80 to-transparent">
                  <div className="flex items-center text-white">
                    <div className="bg-indigo-500/80 p-2 rounded-full mr-3 backdrop-blur-sm">
                      <i className="fas fa-map-marker-alt"></i>
                    </div>
                    <span className="text-sm font-medium">Darjeeling, West Bengal</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="lg:col-span-5">
              <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-100">
                <div className="flex items-center mb-6">
                  <div className="bg-indigo-100 p-3 rounded-full mr-4">
                    <i className="fas fa-quote-left text-indigo-600"></i>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">Our Beginning</h3>
                </div>

                <p className="text-gray-700 leading-relaxed mb-6">
                  At Trypindia, we believe that true travel is about
                  experiences, not just luxury. While exploring the Himalayas and
                  beyond, we realized that local hotels, drivers, and eateries often
                  offer better value and authenticity than the big-names.
                </p>

                <p className="text-gray-700 leading-relaxed mb-6">
                  Frustrated by overpriced tours, we decided to bridge the gap. We personally
                  visited every destination we offer—to handpick trusted local
                  partners. This ensures you enjoy quality, affordable, and genuine
                  travel experiences.
                </p>

                <div className="border-l-4 border-indigo-500 pl-4 italic text-gray-600 mb-6">
                  Because with us, you don't just visit a place—you experience its soul.
                  <span className="text-indigo-600 font-medium"> That's the Trypindia difference.</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex -space-x-2 mr-3">
                      <img src={profile4} alt="Founder" className="w-10 h-10 rounded-full border-2 border-white" />
                      <img src={profile5} alt="Co-founder" className="w-10 h-10 rounded-full border-2 border-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Founded by travelers</p>
                      <p className="text-xs text-gray-600">Est. 2020</p>
                    </div>
                  </div>

                  <a href="#our-mission" className="text-indigo-600 hover:text-indigo-800 text-sm font-medium flex items-center">
                    <span>Learn more</span>
                    <i className="fas fa-arrow-right ml-1 text-xs"></i>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Philosophy Section */}
      <section id="our-mission" className="py-24 px-4 bg-gradient-to-br from-indigo-50 to-blue-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <span className="bg-indigo-100 text-indigo-800 text-xs font-semibold px-3 py-1 rounded-full uppercase tracking-wider">Our Philosophy</span>
            <h2 className="text-3xl md:text-4xl font-bold mt-4 mb-6 text-gray-900">
              The <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-blue-500">Principles</span> That Guide Us
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our commitment to creating travel experiences that are authentic, sustainable, and memorable shapes everything we do.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 items-center">
            <div className="lg:col-span-2 order-2 lg:order-1">
              <div className="grid grid-cols-1 gap-6">
                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-start">
                    <div className="bg-indigo-100 p-3 rounded-full mr-4 flex-shrink-0">
                      <i className="fas fa-compass text-indigo-600"></i>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">Authentic Exploration</h3>
                      <p className="text-gray-600">
                        We believe in showcasing the true essence of North East India, beyond the typical tourist attractions, allowing you to experience the region like a local.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-start">
                    <div className="bg-blue-100 p-3 rounded-full mr-4 flex-shrink-0">
                      <i className="fas fa-handshake text-blue-600"></i>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">Community Partnership</h3>
                      <p className="text-gray-600">
                        We collaborate with local guides, homestays, and businesses to ensure your travel dollars directly support the communities you visit.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-start">
                    <div className="bg-purple-100 p-3 rounded-full mr-4 flex-shrink-0">
                      <i className="fas fa-heart text-purple-600"></i>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">Value Without Compromise</h3>
                      <p className="text-gray-600">
                        We believe quality travel shouldn't break the bank. Our carefully curated experiences offer exceptional value without sacrificing authenticity.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="lg:col-span-3 order-1 lg:order-2 relative">
              <div className="absolute -top-8 -right-8 w-32 h-32 bg-indigo-100 rounded-full z-[-1] opacity-70"></div>
              <div className="absolute -bottom-8 -left-8 w-24 h-24 bg-blue-100 rounded-full z-[-1] opacity-70"></div>

              <div className="relative overflow-hidden rounded-xl shadow-xl h-[400px]">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-600/30 to-blue-500/30 mix-blend-multiply"></div>
                <img
                  src={about_motto}
                  alt="Our Motto"
                  className="w-full h-full object-cover rounded-xl"
                  width="800"
                  height="400"
                  loading="lazy"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-black/50 backdrop-blur-sm p-8 rounded-xl max-w-md text-center">
                    <i className="fas fa-quote-left text-indigo-400 text-3xl mb-4"></i>
                    <p className="text-white text-2xl font-medium italic mb-4">
                      "Exploring Dreams, Creating Memories"
                    </p>
                    <div className="w-24 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 mx-auto"></div>
                    <p className="text-white mt-4">Our promise to every traveler</p>
                  </div>
                </div>
              </div>

              <div className="mt-6 bg-white p-5 rounded-lg shadow-lg border border-gray-100 relative mx-auto max-w-mdz-10">
                <div className="flex items-center">
                  <div className="bg-blue-100 p-2 rounded-full mr-3">
                    <i className="fas fa-lightbulb text-blue-600"></i>
                  </div>
                  <p className="text-gray-700 font-medium">
                    <span className="italic">Real travel is about creating memories, not burning pockets.</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Reviews Section */}
      <section id="testimonials" className="py-24 px-4 bg-white relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute w-[400px] h-[400px] rounded-full bg-blue-50 bottom-[0px] -left-[200px]"></div>
          <div className="absolute w-[60px] h-[60px] rounded-lg bg-indigo-100 top-[30%] left-[5%] rotate-12"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <span className="bg-indigo-100 text-indigo-800 text-xs font-semibold px-3 py-1 rounded-full uppercase tracking-wider">Testimonials</span>
            <h2 className="text-3xl md:text-4xl font-bold mt-4 mb-6 text-gray-900">
              What Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-blue-500">Travelers</span> Say
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Don't just take our word for it. Hear what our travelers have to say about their experiences with Trypindia.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {reviews.map((review, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-xl shadow-lg border border-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-xl relative"
              >
                <div className="absolute top-0 right-0 transform translate-x-2 -translate-y-2">
                  <div className="bg-indigo-100 p-3 rounded-full shadow-md">
                    <i className="fas fa-quote-right text-indigo-600"></i>
                  </div>
                </div>

                <p className="text-gray-700 leading-relaxed mb-6 italic">"{review.review}"</p>

                <div className="flex items-center">
                  <div className="relative">
                    <div className="absolute inset-0 rounded-full bg-gradient-to-r from-indigo-500 to-blue-500 blur-[2px]"></div>
                    <img
                      src={review.image}
                      alt={review.name}
                      className="w-14 h-14 rounded-full object-cover relative z-10 border-2 border-white"
                    />
                  </div>
                  <div className="ml-4">
                    <h3 className="font-semibold text-gray-800 m-0">{review.name}</h3>
                    <div className="flex items-center">
                      <div className="text-indigo-500 mr-2">
                        {[...Array(review.rating)].map((_, i) => (
                          <i key={i} className="fas fa-star text-sm"></i>
                        ))}
                      </div>
                      <span className="text-gray-600 text-sm">{review.date}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-16 text-center">
            <a href="#" className="inline-flex items-center px-6 py-3 bg-indigo-600 text-white font-medium rounded-lg shadow-lg hover:bg-indigo-700 transition-colors duration-300">
              <i className="fas fa-star mr-2"></i>
              <span>Read More Reviews</span>
            </a>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-24 px-4 bg-gradient-to-br from-indigo-50 to-blue-50 relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-indigo-100 rounded-full opacity-50 -mt-24 -mr-24"></div>
        <div className="absolute bottom-0 left-0 w-80 h-80 bg-blue-100 rounded-full opacity-50 -mb-20 -ml-20"></div>
        <div className="absolute bottom-1/3 right-1/4 w-16 h-16 bg-blue-200 rounded-lg -rotate-12"></div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <span className="bg-indigo-100 text-indigo-800 text-xs font-semibold px-3 py-1 rounded-full uppercase tracking-wider">Our Impact</span>
            <h2 className="text-3xl md:text-4xl font-bold mt-4 mb-6 text-gray-900">
              The <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-blue-500">Numbers</span> That Define Us
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We're proud of the impact we've made in connecting travelers with authentic experiences throughout North East India.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { icon: 'fas fa-smile-beam', value: counts.customers, label: 'Happy Travelers', description: 'Satisfied adventurers who explored with us' },
              { icon: 'fas fa-route', value: counts.trips, label: 'Trips Completed', description: 'Successful journeys across North East India' },
              { icon: 'fas fa-handshake', value: counts.localnetwork, label: 'Local Partners', description: 'Trusted local businesses we work with' },
              { icon: 'fas fa-home', value: counts.stays, label: 'Unique Stays', description: 'Carefully selected accommodations' }
            ].map((stat, index) => (
              <div
                key={index}
                className="relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-xl blur-md opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-100 relative z-10 h-full flex flex-col items-center text-center transition-all duration-300 group-hover:-translate-y-1">
                  <div className="mb-6 relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full blur-md opacity-20"></div>
                    <div className="bg-gradient-to-r from-indigo-500 to-blue-500 w-16 h-16 rounded-full flex items-center justify-center relative">
                      <i className={`${stat.icon} text-white text-xl`}></i>
                    </div>
                  </div>

                  <h3 className="text-4xl font-bold mb-2 text-gray-900">
                    {stat.value}<span className="text-indigo-500">+</span>
                  </h3>

                  <p className="text-indigo-600 font-semibold mb-2">
                    {stat.label}
                  </p>

                  <p className="text-gray-500 text-sm">
                    {stat.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-16 text-center">
            <a href="#" className="inline-flex items-center px-6 py-3 bg-white text-indigo-600 font-medium rounded-lg shadow-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-300">
              <i className="fas fa-info-circle mr-2"></i>
              <span>Learn More About Our Impact</span>
            </a>
          </div>
        </div>
      </section>

      {/* Animation keyframes */}
      <style>{`
        @keyframes floatBubble {
          0%, 100% { transform: translate(0, 0) rotate(0deg); }
          25% { transform: translate(50px, 30px) rotate(5deg); }
          50% { transform: translate(0, 50px) rotate(0deg); }
          75% { transform: translate(-50px, 30px) rotate(-5deg); }
        }
        @keyframes fadeInDown {
          from {
            opacity: 0;
            transform: translateY(-30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
};

export default About;