import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Import local images
import forestImg from '../../assets/photos/forest.webp';

const HeroSection = () => {
  const [keywords, setKeywords] = useState('');
  const [destination, setDestination] = useState('Any');
  const [duration, setDuration] = useState('Any');
  const navigate = useNavigate();

  const destinations = [
    'Any', 'Darjeeling', 'Gangtok', 'North Sikkim', 'Pelling', 'Dooars',
    'Kalimpong', 'Lachen', 'Lachung', 'Tsomgo Lake', 'Nathula Pass', 'Yuksom', 'Zuluk'
  ];

  const durations = [
    'Any', '1-2 Days', '3-4 Days', '5-7 Days', '1 Week', '2 Weeks', '1 Month+'
  ];

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    // Navigate to plan page with search parameters
    const params = new URLSearchParams();
    if (keywords.trim()) params.append('keywords', keywords);
    if (destination !== 'Any') params.append('destination', destination.toLowerCase());
    if (duration !== 'Any') params.append('duration', duration);

    navigate(`/plan?${params.toString()}`);
  };

  return (
    <div className="relative min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 min-h-screen">
        {/* Left Side - Content */}
        <div className="flex items-center justify-start p-8 lg:p-16 bg-white relative">
          <div className="max-w-lg">
            {/* Book With Us Badge */}
            <div className="inline-flex items-center bg-cyan-100 text-cyan-700 px-4 py-2 rounded-full text-sm font-medium mb-8">
              Book With Us!
            </div>

            {/* Main Heading */}
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
              Find Next Place
              <br />
              To <span className="text-blue-600">Visit</span>
            </h1>

            {/* Description */}
            <p className="text-lg text-gray-600 mb-4 leading-relaxed">
              Discover amazing places at exclusive deals.
            </p>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              Eat, Shop, Visit interesting places around the world.
            </p>

            {/* Search Form */}
            <form onSubmit={handleSearchSubmit} className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                {/* Keywords Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Keywords
                  </label>
                  <input
                    type="text"
                    placeholder="Type Your Keywords"
                    value={keywords}
                    onChange={(e) => setKeywords(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-200"
                  />
                </div>

                {/* Destination Dropdown */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Destination
                  </label>
                  <select
                    value={destination}
                    onChange={(e) => setDestination(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-200 bg-white"
                  >
                    {destinations.map((dest) => (
                      <option key={dest} value={dest}>
                        {dest}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Duration Dropdown */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Duration
                  </label>
                  <select
                    value={duration}
                    onChange={(e) => setDuration(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-200 bg-white"
                  >
                    {durations.map((dur) => (
                      <option key={dur} value={dur}>
                        {dur}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Search Button */}
              <button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 hover:scale-105 shadow-lg flex items-center justify-center"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
                Search Now
              </button>
            </form>
          </div>
        </div>

        {/* Right Side - Image Only */}
        <div className="relative flex items-center justify-center p-8">
          <div className="relative w-full max-w-lg">
            <img
              src={forestImg}
              alt="Beautiful mountain landscape with lake"
              className="w-full h-96 lg:h-[600px] object-cover rounded-3xl shadow-2xl"
            />
          </div>
        </div>
      </div>

    </div>
  );
};

export default HeroSection;
