import { Link } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// Import local images
import forestImg from '../../assets/photos/forest.webp';
import darjeelingImg from '../../assets/photos/dargeeling2.webp';
import gangtokImg from '../../assets/photos/city_gangtok.webp';



const HeroSection = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const images = [forestImg, darjeelingImg, gangtokImg];

  const destinations = [
    { name: 'Darjeeling', category: 'Hill Station', description: 'Famous for tea gardens and toy train' },
    { name: 'Gangtok', category: 'Capital City', description: 'Modern city with stunning mountain views' },
    { name: 'North Sikkim', category: 'Adventure', description: 'High altitude lakes and monasteries' },
    { name: 'Pelling', category: 'Hill Station', description: 'Perfect views of Kanchenjunga' },
    { name: 'Dooars', category: 'Wildlife', description: 'National parks and wildlife sanctuaries' },
    { name: 'Kalimpong', category: 'Hill Station', description: 'Peaceful town with flower markets' },
    { name: 'Lachen', category: 'Adventure', description: 'Gateway to Gurudongmar Lake' },
    { name: 'Lachung', category: 'Adventure', description: 'Base for Yumthang Valley' },
    { name: 'Tsomgo Lake', category: 'Nature', description: 'Sacred glacial lake' },
    { name: 'Nathula Pass', category: 'Adventure', description: 'Historic Indo-China border' },
    { name: 'Yuksom', category: 'Trekking', description: 'First capital of Sikkim' },
    { name: 'Zuluk', category: 'Offbeat', description: 'Zigzag roads and sunrise views' }
  ];
  const navigate = useNavigate();

  const filteredDestinations = destinations.filter(dest =>
    dest.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    dest.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
    dest.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % images.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);



  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setShowDropdown(e.target.value.length > 0);
    
  };

  const handleDestinationSelect = (destination) => {
    setSearchQuery(destination.name);
    setShowDropdown(false);
    // Navigate to plan page with the selected destination
    navigate(`/plan?destination=${encodeURIComponent(destination.name.toLowerCase())}`);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // If exact match found, use that destination
      const exactMatch = destinations.find(dest =>
        dest.name.toLowerCase() === searchQuery.toLowerCase()
      );
      if (exactMatch) {
        navigate(`/plan?destination=${encodeURIComponent(exactMatch.name.toLowerCase())}`);
      } else {
        navigate(`/plan?destination=${encodeURIComponent(searchQuery.toLowerCase())}`);
      }
    }
  };

  const handleInputFocus = () => {
    if (searchQuery.length > 0) {
      setShowDropdown(true);
    }
  };

  const handleInputBlur = () => {
    // Delay hiding dropdown to allow for clicks
    setTimeout(() => setShowDropdown(false), 200);
  };

  return (
    <div className="relative min-h-screen bg-gray-50">
      {/* Split Screen Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 min-h-screen">
        {/* Left Side - Content */}
        <div className="flex items-center justify-center p-8 lg:p-16 bg-white relative overflow-hidden">
          {/* Decorative Elements */}
          <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-teal-100 to-cyan-100 rounded-full -translate-x-16 -translate-y-16 opacity-60"></div>
          <div className="absolute bottom-0 right-0 w-24 h-24 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-full translate-x-12 translate-y-12 opacity-40"></div>

          <div
            className={`max-w-lg pt-12 transition-all duration-1000 ${
              isVisible
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-8"
            }`}
          >
            {/* Main Heading */}
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight mb-6">
              Budget Journeys
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-indigo-700">
                Rich Experiences
              </span>
            </h1>

            {/* Description */}
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              Skip the tourist traps. Embrace local living, hidden monasteries,
              and scenic beauty — all without stretching your wallet.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <Link
                to="/plan"
                className="group bg-gradient-to-r from-indigo-600 to-indigo-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:from-indigo-700 hover:to-indigo-800 flex items-center justify-center"
              >
                <svg
                  className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                  />
                </svg>
                Start Your Journey
              </Link>
              <Link
                to="/gallery"
                className="group bg-white border-2 border-gray-200 text-gray-700 px-8 py-4 rounded-2xl font-semibold hover:border-indigo-300 hover:text-indigo-600 transition-all duration-300 flex items-center justify-center"
              >
                <svg
                  className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                View Gallery
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 pt-8 border-t border-gray-100">
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-600 mb-1">
                  50+
                </div>
                <div className="text-sm text-gray-500">Destinations</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-700 mb-1">
                  1000+
                </div>
                <div className="text-sm text-gray-500">Happy Travelers</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-600 mb-1">
                  15+
                </div>
                <div className="text-sm text-gray-500">Years Experience</div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Images */}
        <div className="relative bg-gradient-to-br from-teal-50 to-cyan-50 flex items-center justify-center p-8">
          {/* Image Container */}
          <div className="relative w-full max-w-lg">
            {/* Main Image */}
            <div className="relative overflow-hidden rounded-3xl shadow-2xl">
              <img
                src={images[currentImageIndex]}
                alt="North East India"
                className="w-full h-96 lg:h-[500px] object-cover transition-all duration-1000"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>

            {/* Floating Cards */}
            <div className="absolute -top-6 -left-6 bg-white p-4 rounded-2xl shadow-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-gray-700">
                  Live Adventures
                </span>
              </div>
            </div>

            <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-2xl shadow-lg">
              <div className="text-center">
                <div className="text-lg font-bold text-indigo-600">4.9★</div>
                <div className="text-xs text-gray-500">Customer Rating</div>
              </div>
            </div>

            {/* Image Indicators */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
              {images.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    index === currentImageIndex ? "bg-white w-6" : "bg-white/50"
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Background Decorations */}
          <div className="absolute top-20 right-20 w-16 h-16 bg-gradient-to-br from-cyan-200 to-teal-200 rounded-full opacity-60 animate-pulse"></div>
          <div
            className="absolute bottom-32 left-16 w-12 h-12 bg-gradient-to-br from-teal-200 to-emerald-200 rounded-full opacity-40 animate-pulse"
            style={{ animationDelay: "1s" }}
          ></div>
        </div>
      </div>

      {/* Quick Links Section */}
      <div className="bg-white py-8 border-t border-gray-100">
        <div className="max-w-6xl mx-auto px-4">
          {/* Enhanced Search Bar */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="text-center mb-4">
              <h3 className="text-xl font-semibold text-gray-800 mb-2">Find Your Perfect Destination</h3>
              <p className="text-gray-600 text-sm">Search by name, category, or activity type</p>
            </div>
            <form onSubmit={handleSearchSubmit} className="relative">
              <div className="relative">
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Try 'Darjeeling', 'Hill Station', 'Adventure', or 'Tea gardens'..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  onFocus={handleInputFocus}
                  onBlur={handleInputBlur}
                  className="w-full pl-12 pr-28 py-4 bg-white border-2 border-gray-200 rounded-2xl text-gray-800 placeholder-gray-500 focus:border-emerald-500 focus:outline-none transition-all duration-300 shadow-lg hover:shadow-xl"
                />
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                  {searchQuery && (
                    <button
                      type="button"
                      onClick={() => {
                        setSearchQuery('');
                        setShowDropdown(false);
                      }}
                      className="text-gray-400 hover:text-gray-600 p-1.5 rounded-full hover:bg-gray-100 transition-all duration-200"
                      title="Clear search"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                  <button
                    type="submit"
                    className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white p-2.5 rounded-xl transition-all duration-300 hover:scale-105 shadow-md"
                    title="Search destinations"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Enhanced Dropdown */}
              {showDropdown && filteredDestinations.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-3 bg-white border border-gray-200 rounded-2xl shadow-2xl z-50 max-h-80 overflow-y-auto">
                  <div className="p-3 border-b border-gray-100 bg-gray-50 rounded-t-2xl">
                    <p className="text-xs text-gray-600 font-medium">
                      {filteredDestinations.length} destination{filteredDestinations.length !== 1 ? 's' : ''} found
                    </p>
                  </div>
                  {filteredDestinations.map((destination, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => handleDestinationSelect(destination)}
                      className="w-full px-6 py-4 text-left hover:bg-emerald-50 transition-colors duration-200 border-b border-gray-100 last:border-b-0 group"
                    >
                      <div className="flex items-start">
                        <div className="flex-shrink-0 w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-emerald-200 transition-colors duration-200">
                          <svg
                            className="w-5 h-5 text-emerald-600"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4 className="text-gray-900 font-semibold group-hover:text-emerald-700 transition-colors duration-200">
                              {destination.name}
                            </h4>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 group-hover:bg-emerald-100 group-hover:text-emerald-800 transition-colors duration-200">
                              {destination.category}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mt-1 group-hover:text-gray-700 transition-colors duration-200">
                            {destination.description}
                          </p>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}

              {/* No Results Message */}
              {showDropdown && searchQuery && filteredDestinations.length === 0 && (
                <div className="absolute top-full left-0 right-0 mt-3 bg-white border border-gray-200 rounded-2xl shadow-xl z-50 p-6 text-center">
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <p className="text-gray-600 mb-2">No destinations found for "{searchQuery}"</p>
                  <p className="text-sm text-gray-500">Try searching for popular destinations like Darjeeling, Gangtok, or Sikkim</p>
                </div>
              )}
            </form>

            {/* Quick Search Suggestions */}
            {!showDropdown && !searchQuery && (
              <div className="mt-4">
                <p className="text-sm text-gray-500 mb-3 text-center">Popular searches:</p>
                <div className="flex flex-wrap justify-center gap-2">
                  {['Hill Station', 'Adventure', 'Wildlife', 'Trekking', 'Nature'].map((category) => (
                    <button
                      key={category}
                      onClick={() => {
                        setSearchQuery(category);
                        setShowDropdown(true);
                      }}
                      className="px-3 py-1.5 bg-gray-100 hover:bg-emerald-100 text-gray-700 hover:text-emerald-700 rounded-full text-sm font-medium transition-all duration-200 hover:scale-105"
                    >
                      {category}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">
              Popular Destinations
            </h3>
            <p className="text-gray-600">Quick access to our most loved travel packages</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <Link
              to="/plan?destination=darjeeling"
              className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-gray-100"
            >
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-emerald-500 to-teal-500 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg
                  className="w-6 h-6 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 group-hover:text-emerald-700 transition-colors duration-300">Darjeeling</h4>
              <p className="text-sm text-gray-600 mt-1">Tea Gardens & Toy Train</p>
            </Link>

            <Link
              to="/plan?destination=gangtok"
              className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-gray-100"
            >
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-teal-500 to-cyan-500 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg
                  className="w-6 h-6 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 group-hover:text-teal-700 transition-colors duration-300">Gangtok</h4>
              <p className="text-sm text-gray-600 mt-1">Modern City Views</p>
            </Link>

            <Link
              to="/plan?destination=sikkim"
              className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-gray-100"
            >
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg
                  className="w-6 h-6 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 group-hover:text-cyan-700 transition-colors duration-300">North Sikkim</h4>
              <p className="text-sm text-gray-600 mt-1">High Altitude Adventure</p>
            </Link>

            <Link
              to="/plan?destination=pelling"
              className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-gray-100"
            >
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-indigo-500 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg
                  className="w-6 h-6 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 group-hover:text-blue-700 transition-colors duration-300">Pelling</h4>
              <p className="text-sm text-gray-600 mt-1">Kanchenjunga Views</p>
            </Link>
          </div>

          <div className="text-center mt-8">
            <Link
              to="/plan"
              className="group inline-flex items-center bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg"
            >
              <span>View All Destinations</span>
              <svg
                className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
