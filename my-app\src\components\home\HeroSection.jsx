import { useState } from "react";
import { useNavigate } from "react-router-dom";
import forestImg from "../../assets/photos/forest.webp";

const HeroSection = () => {
  const [keywords, setKeywords] = useState("");
  const [destination, setDestination] = useState("Any");
  const [duration, setDuration] = useState("Any");
  const navigate = useNavigate();

  const destinations = [
    "Any",
    "Darjeeling",
    "Gangtok",
    "North Sikkim",
    "Pelling",
    "Dooars",
    "Kalimpong",
    "Lachen",
    "Lachung",
    "Tsomgo Lake",
    "Nathula Pass",
    "Yuksom",
    "Zuluk",
  ];

  const durations = [
    "Any",
    "1-2 Days",
    "3-4 Days",
    "5-7 Days",
    "1 Week",
    "2 Weeks",
    "1 Month+",
  ];

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    const params = new URLSearchParams();
    if (keywords.trim()) params.append("keywords", keywords);
    if (destination !== "Any")
      params.append("destination", destination.toLowerCase());
    if (duration !== "Any") params.append("duration", duration);

    navigate(`/plan?${params.toString()}`);
  };

  return (
    <section className="relative bg-gray-50 overflow-hidden">
      <div className="grid grid-cols-1 lg:grid-cols-2 items-center gap-8 px-8 lg:px-20 py-12 lg:py-20">
        {/* Left Content */}
        <div className="space-y-6">
          {/* Badge */}
          <div className="inline-block bg-green-50 text-green-500 px-5 py-2 rounded-full text-sm font-medium shadow">
            Book With Us!
          </div>

          {/* Heading */}
          <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
            Find Next Place <br />
            To{" "}
            <span className="text-blue-500 tracking-wide inline-block">
              Visit
            </span>
          </h1>

          {/* Description */}
          <p className="text-gray-600 text-lg leading-relaxed">
            Discover amazing places at exclusive deals. <br />
            Eat, Shop, Visit interesting places around the world.
          </p>
        </div>

        {/* Right Image */}
        <div className="flex justify-center lg:justify-end">
          <img
            src={forestImg}
            alt="Beautiful place"
            className="rounded-3xl shadow-lg object-cover h-[450px] w-full max-w-md"
          />
        </div>
      </div>

      {/* Search Bar */}
      <div className="flex justify-center -mt-12 lg:-mt-16 px-4">
        <form
          onSubmit={handleSearchSubmit}
          className="bg-white shadow-lg rounded-2xl p-4 lg:p-6 flex flex-col lg:flex-row items-center gap-4 w-full max-w-5xl"
        >
          {/* Keywords */}
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Keywords
            </label>
            <input
              type="text"
              placeholder="Type Your Keywords"
              value={keywords}
              onChange={(e) => setKeywords(e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 outline-none"
            />
          </div>

          {/* Destination */}
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Destination
            </label>
            <select
              value={destination}
              onChange={(e) => setDestination(e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 outline-none bg-white"
            >
              {destinations.map((dest) => (
                <option key={dest} value={dest}>
                  {dest}
                </option>
              ))}
            </select>
          </div>

          {/* Duration */}
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Duration
            </label>
            <select
              value={duration}
              onChange={(e) => setDuration(e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 outline-none bg-white"
            >
              {durations.map((dur) => (
                <option key={dur} value={dur}>
                  {dur}
                </option>
              ))}
            </select>
          </div>

          {/* Search Button */}
          <div className="flex items-end w-full lg:w-auto">
            <button
              type="submit"
              className="flex items-center justify-center gap-2 bg-blue-500 hover:bg-blue-600 text-white font-semibold px-8 py-3 rounded-lg shadow-lg transition"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
              Search Now
            </button>
          </div>
        </form>
      </div>
    </section>
  );
};

export default HeroSection;
