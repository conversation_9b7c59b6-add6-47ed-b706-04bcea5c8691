import { useState, useEffect, lazy, Suspense } from 'react'
import './App.css'
import '@fortawesome/fontawesome-free/css/all.min.css'
import { Routes, Route, useLocation } from 'react-router-dom';
import { preloadCriticalImages } from './utils/imageOptimization';
import { AuthProvider } from './AuthContext';
import Navbar from './components/Navbar/Navbar';
import Footer from './components/Footer/Footer';
import SEO from './components/common/SEO';

// Lazy load components
const Home = lazy(() => import('./pages/Home'));
const Plan = lazy(() => import('./pages/Plan'));
const About = lazy(() => import('./pages/About'));
const Gallery = lazy(() => import('./pages/Gallery'));
const Contact = lazy(() => import('./pages/contact'));
const LoginSignup = lazy(() => import('./components/Login/LoginSignup'));
const ChatBot = lazy(() => import('./components/ChatBot/ChatBot'));
const MobileLoginPage = lazy(() => import('./pages/MobileLoginPage'));
const PackageDetails = lazy(() => import('./components/Package/Package_Details'));

// Import AOS with reduced features
import AOS from 'aos';
import 'aos/dist/aos.css';

const App = () => {
  const location = useLocation();
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  useEffect(() => {
    // Initialize AOS with optimized settings
    AOS.init({
      duration: 800,
      once: true, // Only animate elements once
      mirror: false, // Don't mirror animations when scrolling up
      offset: 100,
      disable: 'mobile', // Disable on mobile for better performance
    });

    // Preload critical images for better LCP
    preloadCriticalImages([
      // Add paths to critical images here
      '/src/assets/photos/city_gangtok.webp',
      '/src/assets/photos/dargeeling2.webp'
    ]);
  }, []);

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      // Remove console.log to reduce main thread work
    };

    // Call it once on mount
    handleResize();

    // Debounce resize event for better performance
    let resizeTimer;
    const debouncedResize = () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(handleResize, 100);
    };

    window.addEventListener('resize', debouncedResize);
    return () => {
      window.removeEventListener('resize', debouncedResize);
      clearTimeout(resizeTimer);
    };
  }, []);

  // Add this to check if we're on the login page
  const isLoginPage = location.pathname === '/login';
  const isHomePage = location.pathname === '/';
  const isGalleryPage = location.pathname === '/gallery';

  return (
    <AuthProvider>
      <div className={`flex flex-col min-h-screen ${isLoginPage ? 'login-page' : ''}`}>
        {/* Default SEO - will be overridden by page-specific SEO components */}
        <SEO
          title="Trypindia - Gateway to North East India"
          description="Discover the enchanting beauty of North East India with our personalized travel packages. Plan your dream vacation to Darjeeling, Gangtok, and Sikkim."
          keywords="travel, north east india, darjeeling, gangtok, sikkim, travel packages, tourism"
        />

        {/* Navbar */}
        <Navbar />

        {/* Main Content */}
        <main className={`main-content w-full ${isHomePage && isMobile ? 'home-page-mobile' : ''} ${isGalleryPage ? 'gallery-page' : ''}`}>
          <Suspense fallback={
            <div className="flex items-center justify-center min-h-[50vh]">
              <div className="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          }>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/plan" element={<Plan />} />
              <Route path="/package/:id" element={<PackageDetails />} />
              <Route path="/about" element={<About />} />
              <Route path="/gallery" element={<Gallery />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/login" element={isMobile ? <MobileLoginPage /> : <LoginSignup />} />
              <Route path="/mobile-login" element={<MobileLoginPage />} />
            </Routes>
          </Suspense>
        </main>

        {/* ChatBot Component - Lazy loaded */}
        <Suspense fallback={<div></div>}>
          <ChatBot />
        </Suspense>

        {/* Footer */}
        <Footer />
      </div>
    </AuthProvider>
  );
};

export default App;