@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global animations */
@keyframes floatBubble {
  0%, 100% { transform: translate(0, 0); }
  50% { transform: translate(40px, 20px); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes kenBurns {
  0% { transform: scale(1); }
  100% { transform: scale(1.05); }
}

/* Animation utility classes */
.animate-fadeIn {
  animation: fadeIn 0.6s ease-out;
}

.animate-floatBubble {
  animation: floatBubble 25s infinite ease-in-out;
}

.animate-floatBubble-reverse {
  animation: floatBubble 30s infinite ease-in-out reverse;
}

.animate-kenBurns {
  animation: kenBurns 10s ease-in-out infinite alternate;
}

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #fffbfb;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  padding: 0;
  min-width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
